<?php
require_once 'frontend/src/pages/admin/config.php';

try {
    $pdo = getConnection();
    $stmt = $pdo->query('SELECT id, title, image FROM news ORDER BY created_at DESC LIMIT 10');
    $news = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Recent news with images:\n";
    foreach ($news as $item) {
        echo "ID: {$item['id']}, Title: {$item['title']}, Image: {$item['image']}\n";
        
        // Check if file exists
        if ($item['image']) {
            $filename = '';
            if (strpos($item['image'], '/uploads/') === 0) {
                $filename = str_replace('/uploads/', '', $item['image']);
            } else if (!strpos($item['image'], '/')) {
                $filename = $item['image'];
            } else {
                $filename = basename($item['image']);
            }
            
            $filePath = __DIR__ . '/uploads/' . $filename;
            $exists = file_exists($filePath);
            echo "  File exists: " . ($exists ? 'YES' : 'NO') . " - Path: $filePath\n";
        }
        echo "\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
